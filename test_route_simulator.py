#!/usr/bin/env python3
"""
Zenoh Route Simulator для тестирования виджета карты.

Этот скрипт имитирует:
1. Топик driver_status с прогрессом по маршруту
2. Топик drive_action с полной траекторией
3. Топик position с движением по маршруту

Использует Zenoh для публикации данных в формате drill_msgs через zenoh_client.
"""

import math
import time
import sys
import os
from typing import List, Dict, Any

# Add parent directory to path to import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.zenoh_client import ZenohClient
    print("ZenohClient imported successfully")
except ImportError as e:
    print(f"Error importing ZenohClient: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class ZenohRouteSimulator:
    def __init__(self):
        """Initialize Zenoh simulator."""
        # Namespace
        self.namespace = "test_vehicle"
        
        # Create Zenoh client
        self.client = ZenohClient()
        self.client.start()
        
        # Create service server for GetCurrentDriveAction
        self.client.create_service_server(
            service_name="get_current_drive_action",
            service_type="drill_msgs/srv/GetCurrentDriveAction",
            namespace=self.namespace,
            callback=self.handle_get_drive_action_request
        )
        
        # Simulation state
        self.current_action_id = 1
        self.current_segment_id = 0
        self.current_point_id = 0
        self.current_position = [0.0, 0.0, 0.0]  # x, y, yaw
        
        # Create test routes
        self.test_routes = self.create_test_routes()
        
        # Running flag
        self.running = False
        
        print("Zenoh Route Simulator initialized")
        print(f"Created {len(self.test_routes)} test route segments")
        print(f"Publishing to namespace: {self.namespace}")
    
    def create_test_routes(self) -> List[Dict[str, Any]]:
        """Create test routes for demonstration."""
        routes = []
        
        # Segment 1: Straight line east
        segment1 = {
            "points": []
        }
        for i in range(10):
            point = {
                "x": float(i * 2),  # Every 2 meters
                "y": 0.0,
                "speed": 1.0,
                "roughness": 0.0
            }
            segment1["points"].append(point)
        segment1["completed_pt_cnt"] = 0
        routes.append(segment1)
        
        # Segment 2: Turn north
        segment2 = {
            "points": []
        }
        for i in range(8):
            point = {
                "x": 18.0,  # End of first segment
                "y": float(i * 2),
                "speed": 1.0,
                "roughness": 0.0
            }
            segment2["points"].append(point)
        segment2["completed_pt_cnt"] = 0
        routes.append(segment2)
        
        # Segment 3: Diagonal northwest
        segment3 = {
            "points": []
        }
        for i in range(6):
            point = {
                "x": 18.0 - float(i * 1.5),
                "y": 14.0 + float(i * 1.5),
                "speed": 1.0,
                "roughness": 0.0
            }
            segment3["points"].append(point)
        segment3["completed_pt_cnt"] = 0
        routes.append(segment3)
        
        return routes
    
    def handle_get_drive_action_request(self, request):
        """Handle GetCurrentDriveAction service request."""
        print(f"Received GetCurrentDriveAction request for action_id: {getattr(request, 'action_id', 'N/A')}")
        
        # Create response with current drive action
        response = self.client.store.types["drill_msgs/srv/GetCurrentDriveAction"].Response()
        
        # Create path_segments from test routes
        path_segments = []
        for i, route_data in enumerate(self.test_routes):
            # Create points for this segment
            points = []
            for point_data in route_data["points"]:
                # Create PathPoint
                point = self.client.store.types["drill_msgs/msg/PathPoint"](
                    x=point_data["x"],
                    y=point_data["y"],
                    speed=point_data["speed"],
                    roughness=point_data["roughness"]
                )
                points.append(point)
            
            # Create Path segment
            segment = self.client.store.types["drill_msgs/msg/Path"](
                points=points,
                completed_pt_cnt=route_data["completed_pt_cnt"]
            )
            path_segments.append(segment)
        
        # Create DriveAction for response
        drive_action = self.client.store.types["drill_msgs/msg/DriveAction"](
            action_id=self.current_action_id,
            path_segments=path_segments
        )
        
        response.drive_action = drive_action
        response.success = True
        response.message = "Drive action retrieved successfully"
        
        print(f"Responding with action_id: {self.current_action_id}, segments: {len(path_segments)}")
        return response
    
    def start(self):
        """Start the simulator."""
        self.running = True
        print("Zenoh Route Simulator started! Press Ctrl+C to stop.")
    
    def stop(self):
        """Stop the simulator."""
        self.running = False
        self.client.shutdown()
        print("Zenoh Route Simulator stopped")
    
    def publish_position(self):
        """Publish current robot position."""
        if not self.running:
            return
        
        # Get current target point
        target_point = self.get_current_target_point()
        if target_point:
            # Smoothly move towards target point
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]
            
            # Simple movement logic
            speed = 0.1  # m/s
            if abs(dx) > 0.1 or abs(dy) > 0.1:
                distance = math.sqrt(dx*dx + dy*dy)
                self.current_position[0] += (dx / distance) * speed
                self.current_position[1] += (dy / distance) * speed
                self.current_position[2] = math.atan2(dy, dx)  # yaw
        
        # Publish position message
        self.client.publish(
            key_expr="position",
            msg_type="drill_msgs/msg/Position",
            namespace=self.namespace,
            x=self.current_position[0],
            y=self.current_position[1],
            z=0.0,
            yaw=self.current_position[2],
            is_reliable=True
        )
    
    def publish_driver_status(self):
        """Publish driver status."""
        if not self.running:
            return
        
        # Publish DriveStatus message
        self.client.publish(
            key_expr="driver_status",
            msg_type="drill_msgs/msg/DriveStatus",
            namespace=self.namespace,
            cur_action_id=self.current_action_id,
            last_action_id=self.current_action_id - 1 if self.current_action_id > 1 else 0,
            cur_segment_id=self.current_segment_id,
            cur_point_id=self.current_point_id,
            status="executing"
        )
        
        print(f'Status: action_id={self.current_action_id}, '
              f'segment={self.current_segment_id}, point={self.current_point_id}')
    
    def update_progress(self):
        """Update movement progress along the route."""
        # Check if we reached current point
        target_point = self.get_current_target_point()
        if target_point:
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)
            
            # If close to point, advance to next
            if distance < 0.5:  # 50 cm
                self.advance_to_next_point()
    
    def get_current_target_point(self) -> Dict[str, float]:
        """Get current target point."""
        if (self.current_segment_id < len(self.test_routes) and
            self.current_point_id < len(self.test_routes[self.current_segment_id]["points"])):
            return self.test_routes[self.current_segment_id]["points"][self.current_point_id]
        return None
    
    def advance_to_next_point(self):
        """Advance to next point in the route."""
        current_segment = self.test_routes[self.current_segment_id]
        
        # Update completed point count
        current_segment["completed_pt_cnt"] = self.current_point_id + 1
        
        # Advance to next point in current segment
        if self.current_point_id < len(current_segment["points"]) - 1:
            self.current_point_id += 1
            print(f'Advanced to point {self.current_point_id}')
        
        # Advance to next segment
        elif self.current_segment_id < len(self.test_routes) - 1:
            self.current_segment_id += 1
            self.current_point_id = 0
            print(f'Advanced to segment {self.current_segment_id}')
        
        # Completed all segments - start new action
        else:
            self.current_action_id += 1
            self.current_segment_id = 0
            self.current_point_id = 0
            print(f'Started new action {self.current_action_id} - routes updated in service')
            
            # Reset position for new route
            self.current_position = [0.0, 0.0, 0.0]
            
            # Reset completed counts
            for segment in self.test_routes:
                segment["completed_pt_cnt"] = 0
            
    def run(self):
        """Main loop."""
        try:
            position_interval = 0.1  # 10 Hz
            status_interval = 1.0    # 1 Hz
            progress_interval = 3.0  # Every 3 seconds
            
            last_position_time = 0
            last_status_time = 0
            last_progress_time = 0
            
            while self.running:
                current_time = time.time()
                
                # Publish position
                if current_time - last_position_time >= position_interval:
                    self.publish_position()
                    last_position_time = current_time
                
                # Publish status
                if current_time - last_status_time >= status_interval:
                    self.publish_driver_status()
                    last_status_time = current_time
                
                # Update progress
                if current_time - last_progress_time >= progress_interval:
                    self.update_progress()
                    last_progress_time = current_time
                
                # Small sleep to prevent busy loop
                time.sleep(0.01)
                
        except KeyboardInterrupt:
            print("\nStopping simulator...")


def main():
    """Main function."""
    # Create and start simulator
    simulator = ZenohRouteSimulator()
    simulator.start()
    
    try:
        simulator.run()
    finally:
        simulator.stop()


if __name__ == '__main__':
    main()
